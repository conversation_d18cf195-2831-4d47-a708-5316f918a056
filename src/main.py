"""
Main FastAPI application for LLM RAG Codebase Query System.
"""

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import Any, Dict, Optional

import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

from .agents import (
    AgentContext,
    AgentFactory,
    AgentRegistry,
    AgentType,
    ConversationContext,
    Message,
    MessageRole,
)
from .config import get_settings
from .ingestion.integration_example import IntegratedIngestionPipeline

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get settings
settings = get_settings()

# Global variables for shared services
agent_factory: Optional[AgentFactory] = None
agent_registry: Optional[AgentRegistry] = None
ingestion_pipeline: Optional[IntegratedIngestionPipeline] = None


# Pydantic Models for API
class QueryRequest(BaseModel):
    """Request model for query endpoint."""

    query: str = Field(..., description="Natural language query to process")
    session_id: Optional[str] = Field(None, description="Session ID for conversation context")
    user_id: Optional[str] = Field(None, description="User ID for personalization")
    context_filters: Optional[Dict[str, Any]] = Field(None, description="Additional context filters")
    repository: Optional[str] = Field(None, description="Target repository for query")


class QueryResponse(BaseModel):
    """Response model for query endpoint."""

    result_markdown: str = Field(..., description="Formatted response in Markdown")
    structured: Dict[str, Any] = Field(..., description="Structured response data")
    agent_type: str = Field(..., description="Primary agent that processed the query")
    confidence: float = Field(..., description="Confidence score of the response")
    sources: list[str] = Field(default_factory=list, description="Source citations")
    processing_time: Optional[float] = Field(None, description="Processing time in seconds")
    session_id: str = Field(..., description="Session ID for conversation tracking")


class IngestionRequest(BaseModel):
    """Request model for ingestion endpoint."""

    repository_url: str = Field(..., description="GitHub repository URL to ingest")
    branch: Optional[str] = Field("main", description="Branch to ingest")
    commit_sha: Optional[str] = Field(None, description="Specific commit to ingest")
    force_refresh: bool = Field(False, description="Force refresh of existing data")


class IngestionResponse(BaseModel):
    """Response model for ingestion endpoint."""

    status: str = Field(..., description="Ingestion status")
    repository: str = Field(..., description="Repository that was ingested")
    processed_files: int = Field(..., description="Number of files processed")
    chunks_created: int = Field(..., description="Number of chunks created")
    embeddings_generated: int = Field(..., description="Number of embeddings generated")
    processing_time: float = Field(..., description="Total processing time in seconds")


class StatusResponse(BaseModel):
    """Response model for status endpoint."""

    api: str = Field(..., description="API status")
    agents: Dict[str, str] = Field(..., description="Agent system status")
    vector_store: str = Field(..., description="Vector store status")
    ingestion_pipeline: str = Field(..., description="Ingestion pipeline status")
    active_sessions: int = Field(..., description="Number of active conversation sessions")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan events."""
    global agent_factory, agent_registry, ingestion_pipeline

    logger.info("Starting up LLM RAG Codebase Query System...")

    try:
        # Initialize shared services
        agent_factory = AgentFactory(settings)
        agent_registry = AgentRegistry(agent_factory)
        ingestion_pipeline = IntegratedIngestionPipeline(settings)

        # Initialize async services
        await agent_factory.initialize_shared_services()

        logger.info("✅ Application startup completed successfully")

        yield

    except Exception as e:
        logger.error(f"❌ Failed to start application: {e}")
        raise
    finally:
        # Cleanup
        logger.info("Shutting down application...")
        if agent_factory:
            await agent_factory.shutdown_shared_services()
        logger.info("✅ Application shutdown completed")


# Create FastAPI app with lifespan management
app = FastAPI(
    title="LLM RAG Codebase Query System",
    description="A sophisticated multi-agent RAG system for querying GitHub repository codebases",
    version="0.1.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://frontend:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Helper functions
async def get_or_create_session(session_id: Optional[str], user_id: Optional[str]) -> ConversationContext:
    """Get existing session or create a new one."""
    if not agent_factory:
        raise HTTPException(status_code=500, detail="Agent factory not initialized")

    context_manager = agent_factory.context_manager

    if session_id:
        # Try to get existing session
        existing_context = await context_manager.get_context(session_id)
        if existing_context:
            return existing_context

    # Create new session
    return await context_manager.create_session(user_id=user_id)


# API Endpoints
@app.get("/")
async def root() -> Dict[str, str]:
    """Root endpoint."""
    return {"message": "LLM RAG Codebase Query System API"}


@app.get("/health")
async def health_check() -> Dict[str, Any]:
    """Health check endpoint."""
    return {
        "status": "healthy",
        "service": "llm-rag-backend",
        "version": "0.1.0",
        "environment": settings.environment,
    }


@app.get("/api/status")
async def api_status() -> StatusResponse:
    """Enhanced API status endpoint with agent system health."""
    try:
        # Check agent system status
        agents_status = {}
        if agent_registry:
            available_agents = agent_factory.get_available_agents() if agent_factory else {}
            for agent_type in available_agents:
                try:
                    # Try to get agent (this will create it if needed)
                    agent = agent_registry.get_agent(agent_type)
                    agents_status[agent_type.value] = "operational"
                except Exception as e:
                    agents_status[agent_type.value] = f"error: {str(e)}"
        else:
            agents_status = {"status": "not_initialized"}

        # Check vector store status
        vector_store_status = "operational"
        if ingestion_pipeline:
            try:
                # TODO: Add actual vector store health check
                vector_store_status = "operational"
            except Exception:
                vector_store_status = "error"
        else:
            vector_store_status = "not_initialized"

        # Check ingestion pipeline status
        pipeline_status = "operational" if ingestion_pipeline else "not_initialized"

        # Get active sessions count
        active_sessions = 0
        if agent_factory and agent_factory.context_manager:
            try:
                # TODO: Add method to get active session count
                active_sessions = 0  # Placeholder
            except Exception:
                active_sessions = -1

        return StatusResponse(
            api="operational",
            agents=agents_status,
            vector_store=vector_store_status,
            ingestion_pipeline=pipeline_status,
            active_sessions=active_sessions,
        )

    except Exception as e:
        logger.error(f"Status check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Status check failed: {str(e)}")


@app.post("/api/query")
async def process_query(request: QueryRequest) -> QueryResponse:
    """Process a natural language query using the multi-agent system."""
    if not agent_factory or not agent_registry:
        raise HTTPException(status_code=500, detail="Agent system not initialized")

    try:
        logger.info(f"Processing query: {request.query[:100]}...")

        # Get or create conversation session
        conversation_context = await get_or_create_session(request.session_id, request.user_id)

        # Add user message to conversation history
        user_message = Message(
            role=MessageRole.USER,
            content=request.query,
            metadata={"repository": request.repository, "filters": request.context_filters}
        )
        await agent_factory.context_manager.add_message(conversation_context.session_id, user_message)

        # Create agent context
        agent_context = AgentContext(
            conversation_context=conversation_context,
            repository_context={"repository": request.repository} if request.repository else None,
            execution_metadata={
                "filters": request.context_filters or {},
                "user_id": request.user_id,
            }
        )

        # Get orchestrator agent
        orchestrator = agent_registry.get_agent(AgentType.ORCHESTRATOR)

        # Process query through orchestrator
        response = await orchestrator.process_query(request.query, agent_context)

        # Add assistant response to conversation history
        assistant_message = Message(
            role=MessageRole.ASSISTANT,
            content=response.content,
            metadata={
                "agent_type": response.agent_type.value,
                "confidence": response.confidence,
                "sources": response.sources,
            }
        )
        await agent_factory.context_manager.add_message(conversation_context.session_id, assistant_message)

        # Format structured response
        structured_data = {
            "agent_type": response.agent_type.value,
            "confidence": response.confidence,
            "metadata": response.metadata,
            "sources": response.sources,
            "session_id": conversation_context.session_id,
        }

        # Add task plan data if available (for Task Planner responses)
        if "task_plan" in response.metadata:
            structured_data["task_plan"] = response.metadata["task_plan"]

        # Add architecture analysis if available (for Technical Architect responses)
        if "architecture_analysis" in response.metadata:
            structured_data["architecture_analysis"] = response.metadata["architecture_analysis"]

        logger.info(f"Query processed successfully by {response.agent_type.value}")

        return QueryResponse(
            result_markdown=response.content,
            structured=structured_data,
            agent_type=response.agent_type.value,
            confidence=response.confidence,
            sources=response.sources,
            processing_time=response.processing_time,
            session_id=conversation_context.session_id,
        )

    except Exception as e:
        logger.error(f"Query processing failed: {e}")
        raise HTTPException(status_code=500, detail=f"Query processing failed: {str(e)}")


@app.post("/api/ingest")
async def ingest_repository(request: IngestionRequest) -> IngestionResponse:
    """Ingest a GitHub repository into the knowledge base."""
    if not ingestion_pipeline:
        raise HTTPException(status_code=500, detail="Ingestion pipeline not initialized")

    try:
        logger.info(f"Starting ingestion for repository: {request.repository_url}")

        import time
        start_time = time.time()

        # Run the ingestion pipeline
        result = await ingestion_pipeline.process_repository(
            repository_url=request.repository_url,
            branch=request.branch,
        )

        processing_time = time.time() - start_time

        logger.info(f"Ingestion completed for {request.repository_url} in {processing_time:.2f}s")

        return IngestionResponse(
            status="completed",
            repository=request.repository_url,
            processed_files=result.get("files_processed", 0),
            chunks_created=result.get("chunks_generated", 0),
            embeddings_generated=result.get("embeddings_created", 0),
            processing_time=processing_time,
        )

    except Exception as e:
        logger.error(f"Repository ingestion failed: {e}")
        raise HTTPException(status_code=500, detail=f"Ingestion failed: {str(e)}")


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler."""
    logger.error(f"Global exception: {exc}")
    return JSONResponse(status_code=500, content={"detail": "Internal server error"})


if __name__ == "__main__":
    uvicorn.run(
        "src.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True if settings.environment == "development" else False,
        log_level="info",
    )
