"""
Integration tests for API endpoints and multi-agent workflows.

This module tests the complete API integration including:
- FastAPI application startup and shutdown
- Query processing through the orchestrator
- Multi-agent coordination
- Response formatting and validation
"""

import asyncio
import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, MagicMock, patch

from src.main import app
from src.agents import AgentType, AgentResponse, AgentContext, ConversationContext
from src.config import get_settings


class TestAPIIntegration:
    """Test API integration with multi-agent system."""

    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)

    @pytest.fixture
    def mock_agent_factory(self):
        """Create mock agent factory for testing."""
        mock_factory = MagicMock()
        mock_context_manager = AsyncMock()
        mock_session = ConversationContext(session_id="test-session-123")
        
        mock_context_manager.create_session.return_value = mock_session
        mock_context_manager.get_context.return_value = mock_session
        mock_context_manager.add_message.return_value = mock_session
        
        mock_factory.context_manager = mock_context_manager
        return mock_factory

    @pytest.fixture
    def mock_orchestrator(self):
        """Create mock orchestrator agent."""
        mock_orchestrator = AsyncMock()
        mock_response = AgentResponse(
            agent_type=AgentType.RAG_RETRIEVAL,
            content="# OAuth Authentication\n\nOAuth is an authorization framework...",
            confidence=0.9,
            sources=["docs/auth.md", "src/auth/oauth.py"],
            processing_time=1.5,
            metadata={"query_type": "factual", "search_results_count": 3}
        )
        mock_orchestrator.process_query.return_value = mock_response
        return mock_orchestrator

    @pytest.fixture
    def mock_ingestion_pipeline(self):
        """Create mock ingestion pipeline."""
        mock_pipeline = AsyncMock()
        mock_result = {
            "repository_url": "https://github.com/test/repo",
            "branch": "main",
            "files_processed": 25,
            "chunks_generated": 150,
            "embeddings_created": 150,
            "storage_success": True,
        }
        mock_pipeline.process_repository.return_value = mock_result
        return mock_pipeline

    def test_root_endpoint(self, client):
        """Test root endpoint."""
        response = client.get("/")
        assert response.status_code == 200
        assert response.json() == {"message": "LLM RAG Codebase Query System API"}

    def test_health_endpoint(self, client):
        """Test health check endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "llm-rag-backend"
        assert "version" in data
        assert "environment" in data

    @patch('src.main.agent_factory')
    @patch('src.main.agent_registry')
    def test_status_endpoint_with_agents(self, mock_registry, mock_factory, client):
        """Test status endpoint with agent system."""
        # Mock agent factory
        mock_factory.get_available_agents.return_value = {
            AgentType.RAG_RETRIEVAL: {"name": "RAG Agent"},
            AgentType.ORCHESTRATOR: {"name": "Orchestrator"},
        }
        
        # Mock agent registry
        mock_agent = MagicMock()
        mock_registry.get_agent.return_value = mock_agent
        
        response = client.get("/api/status")
        assert response.status_code == 200
        data = response.json()
        assert data["api"] == "operational"
        assert "agents" in data
        assert "vector_store" in data
        assert "ingestion_pipeline" in data
        assert "active_sessions" in data

    @patch('src.main.agent_factory')
    @patch('src.main.agent_registry')
    async def test_query_endpoint_success(self, mock_registry, mock_factory, mock_orchestrator, mock_agent_factory, client):
        """Test successful query processing."""
        # Setup mocks
        mock_factory = mock_agent_factory
        mock_registry.get_agent.return_value = mock_orchestrator
        
        # Test query request
        query_data = {
            "query": "What is OAuth authentication?",
            "session_id": "test-session",
            "user_id": "test-user"
        }
        
        with patch('src.main.agent_factory', mock_factory), \
             patch('src.main.agent_registry', mock_registry):
            response = client.post("/api/query", json=query_data)
        
        assert response.status_code == 200
        data = response.json()
        
        # Validate response structure
        assert "result_markdown" in data
        assert "structured" in data
        assert "agent_type" in data
        assert "confidence" in data
        assert "sources" in data
        assert "processing_time" in data
        assert "session_id" in data
        
        # Validate content
        assert data["agent_type"] == "rag_retrieval"
        assert data["confidence"] == 0.9
        assert len(data["sources"]) == 2
        assert "OAuth" in data["result_markdown"]

    @patch('src.main.ingestion_pipeline')
    async def test_ingest_endpoint_success(self, mock_pipeline, mock_ingestion_pipeline, client):
        """Test successful repository ingestion."""
        mock_pipeline = mock_ingestion_pipeline
        
        ingest_data = {
            "repository_url": "https://github.com/test/repo",
            "branch": "main",
            "force_refresh": False
        }
        
        response = client.post("/api/ingest", json=ingest_data)
        assert response.status_code == 200
        data = response.json()
        
        # Validate response structure
        assert data["status"] == "completed"
        assert data["repository"] == "https://github.com/test/repo"
        assert data["processed_files"] == 25
        assert data["chunks_created"] == 150
        assert data["embeddings_generated"] == 150
        assert "processing_time" in data

    def test_query_endpoint_missing_agents(self, client):
        """Test query endpoint when agent system is not initialized."""
        query_data = {
            "query": "What is OAuth authentication?"
        }
        
        response = client.post("/api/query", json=query_data)
        assert response.status_code == 500
        assert "Agent system not initialized" in response.json()["detail"]

    def test_ingest_endpoint_missing_pipeline(self, client):
        """Test ingest endpoint when pipeline is not initialized."""
        ingest_data = {
            "repository_url": "https://github.com/test/repo"
        }
        
        response = client.post("/api/ingest", json=ingest_data)
        assert response.status_code == 500
        assert "Ingestion pipeline not initialized" in response.json()["detail"]

    def test_query_endpoint_validation(self, client):
        """Test query endpoint input validation."""
        # Missing required field
        response = client.post("/api/query", json={})
        assert response.status_code == 422
        
        # Invalid data types
        response = client.post("/api/query", json={"query": 123})
        assert response.status_code == 422

    def test_ingest_endpoint_validation(self, client):
        """Test ingest endpoint input validation."""
        # Missing required field
        response = client.post("/api/ingest", json={})
        assert response.status_code == 422
        
        # Invalid URL format (this would be caught by business logic, not validation)
        response = client.post("/api/ingest", json={"repository_url": "not-a-url"})
        assert response.status_code == 500  # Would fail in processing


class TestMultiAgentWorkflow:
    """Test multi-agent workflow coordination."""

    @pytest_asyncio.fixture
    async def agent_factory(self):
        """Create real agent factory for integration testing."""
        from src.agents import AgentFactory
        from src.config import get_settings

        settings = get_settings()
        factory = AgentFactory(settings)
        await factory.initialize_shared_services()

        yield factory

        await factory.shutdown_shared_services()

    @pytest.mark.asyncio
    async def test_orchestrator_agent_creation(self, agent_factory):
        """Test orchestrator agent creation and basic functionality."""
        from src.agents.factory import AgentRegistry
        
        registry = AgentRegistry(agent_factory)
        orchestrator = registry.get_agent(AgentType.ORCHESTRATOR)
        
        assert orchestrator is not None
        assert orchestrator.agent_type == AgentType.ORCHESTRATOR
        
        # Test basic query handling capability
        context = await agent_factory.context_manager.create_session(user_id="test")
        agent_context = AgentContext(conversation_context=context)
        
        confidence = orchestrator.can_handle_query("Test query", agent_context)
        assert confidence > 0.0

    @pytest.mark.asyncio
    async def test_agent_routing_workflow(self, agent_factory):
        """Test complete agent routing workflow."""
        from src.orchestrator.classifier import QueryClassifier
        from src.orchestrator.router import AgentRouter
        from src.agents.base import AgentContext
        
        # Create components
        classifier = QueryClassifier()
        router = AgentRouter(classifier)
        
        # Create context
        session = await agent_factory.context_manager.create_session(user_id="test")
        agent_context = AgentContext(conversation_context=session)
        
        # Test different query types with expected routing behavior
        test_queries = [
            # High confidence queries should route to classified agent
            ("What is OAuth authentication?", AgentType.RAG_RETRIEVAL, True),
            # Low confidence queries may use fallback routing
            ("Design a user authentication system", AgentType.TECHNICAL_ARCHITECT, False),
            ("Break down the login feature implementation", AgentType.TASK_PLANNER, False),
        ]

        for query, expected_agent, expect_exact_match in test_queries:
            # Classify query
            classification = classifier.classify_query(query)
            assert classification.agent_type == expected_agent

            # Route query
            available_agents = {AgentType.RAG_RETRIEVAL, AgentType.TECHNICAL_ARCHITECT, AgentType.TASK_PLANNER}
            decision = router.route_query(query, agent_context, available_agents)

            if expect_exact_match:
                # High confidence queries should route exactly
                assert decision.primary_agent == expected_agent
            else:
                # Low confidence queries may use fallback (RAG_RETRIEVAL) or expected agent
                assert decision.primary_agent in [expected_agent, AgentType.RAG_RETRIEVAL]
                # But the expected agent should be in fallback chain
                if decision.primary_agent != expected_agent:
                    assert expected_agent in decision.fallback_agents

            assert decision.confidence > 0.0
            assert len(decision.reasoning) > 0

    @pytest.mark.asyncio
    async def test_conversation_context_management(self, agent_factory):
        """Test conversation context management across multiple queries."""
        context_manager = agent_factory.context_manager
        
        # Create session
        session = await context_manager.create_session(user_id="test-user")
        assert session.session_id is not None
        assert session.user_id == "test-user"
        
        # Add messages
        from src.agents.base import Message, MessageRole
        
        user_msg = Message(role=MessageRole.USER, content="What is OAuth?")
        updated_session = await context_manager.add_message(session.session_id, user_msg)
        
        assert len(updated_session.conversation_history) == 1
        assert updated_session.conversation_history[0].content == "What is OAuth?"
        
        # Add assistant response
        assistant_msg = Message(role=MessageRole.ASSISTANT, content="OAuth is an authorization framework...")
        updated_session = await context_manager.add_message(session.session_id, assistant_msg)
        
        assert len(updated_session.conversation_history) == 2
        
        # Retrieve session
        retrieved_session = await context_manager.get_context(session.session_id)
        assert retrieved_session is not None
        assert len(retrieved_session.conversation_history) == 2

    @pytest.mark.asyncio
    async def test_agent_response_synthesis(self, agent_factory):
        """Test response synthesis from multiple agents."""
        from src.orchestrator.synthesizer import ResponseSynthesizer
        from src.agents.formatters import MarkdownFormatter
        
        formatter = MarkdownFormatter()
        synthesizer = ResponseSynthesizer(formatter)
        
        # Create mock responses from different agents
        responses = [
            AgentResponse(
                agent_type=AgentType.TECHNICAL_ARCHITECT,
                content="## Architecture Analysis\nThe system uses OAuth 2.0...",
                confidence=0.9,
                sources=["docs/architecture.md"]
            ),
            AgentResponse(
                agent_type=AgentType.TASK_PLANNER,
                content="## Implementation Tasks\n1. Setup OAuth provider...",
                confidence=0.8,
                sources=["docs/tasks.md"]
            )
        ]
        
        # Synthesize responses
        result = synthesizer.synthesize_responses(responses, "multi_agent", "How to implement OAuth?")
        
        assert result.content is not None
        assert len(result.content) > 0
        assert result.confidence > 0.0
        assert len(result.sources) >= 2
        assert "Architecture Analysis" in result.content
        assert "Implementation Tasks" in result.content

    def test_error_handling_and_fallbacks(self, agent_factory):
        """Test error handling and fallback mechanisms."""
        from src.orchestrator.router import AgentRouter
        from src.orchestrator.classifier import QueryClassifier
        
        classifier = QueryClassifier()
        router = AgentRouter(classifier)
        
        # Test with limited available agents
        limited_agents = {AgentType.RAG_RETRIEVAL}  # Only RAG agent available
        
        # This should still work with fallback
        session = asyncio.run(agent_factory.context_manager.create_session(user_id="test"))
        agent_context = AgentContext(conversation_context=session)
        
        decision = router.route_query("Design a complex system", agent_context, limited_agents)
        
        # Should fallback to available agent
        assert decision.primary_agent == AgentType.RAG_RETRIEVAL
        assert decision.confidence > 0.0


class TestEndToEndConversationSimulation:
    """Test end-to-end conversation simulation with multi-turn interactions."""

    @pytest_asyncio.fixture
    async def agent_factory(self):
        """Create real agent factory for conversation testing."""
        from src.agents import AgentFactory
        from src.config import get_settings

        settings = get_settings()
        factory = AgentFactory(settings)
        await factory.initialize_shared_services()

        yield factory

        await factory.shutdown_shared_services()

    @pytest.mark.asyncio
    async def test_multi_turn_conversation_context(self, agent_factory):
        """Test multi-turn conversation with context preservation."""
        from src.agents.factory import AgentRegistry
        from src.agents.base import Message, MessageRole

        registry = AgentRegistry(agent_factory)
        orchestrator = registry.get_agent(AgentType.ORCHESTRATOR)

        # Start a conversation session
        session = await agent_factory.context_manager.create_session(user_id="test-user")
        agent_context = AgentContext(conversation_context=session)

        # Turn 1: Ask about OAuth
        query1 = "What is OAuth authentication?"
        response1 = await orchestrator.process_query(query1, agent_context)

        assert response1 is not None
        assert "OAuth" in response1.content
        # Orchestrator returns its own type but includes primary agent in metadata
        assert response1.agent_type == AgentType.ORCHESTRATOR
        assert response1.metadata["primary_agent"] == AgentType.RAG_RETRIEVAL.value

        # Note: Orchestrator automatically adds messages to conversation history
        # No need to manually add them here

        # Turn 2: Follow-up question (should have context)
        query2 = "How would you implement it in a web application?"
        updated_session = await agent_factory.context_manager.get_context(session.session_id)
        agent_context.conversation_context = updated_session

        response2 = await orchestrator.process_query(query2, agent_context)

        assert response2 is not None
        assert len(response2.content) > 0
        # Orchestrator returns its own type but includes primary agent in metadata
        assert response2.agent_type == AgentType.ORCHESTRATOR
        # Should route to Technical Architect or use fallback
        assert response2.metadata["primary_agent"] in [AgentType.TECHNICAL_ARCHITECT.value, AgentType.RAG_RETRIEVAL.value]

        # Verify conversation history is preserved
        final_session = await agent_factory.context_manager.get_context(session.session_id)
        assert len(final_session.conversation_history) >= 2
        assert final_session.conversation_history[0].content == query1

    @pytest.mark.asyncio
    async def test_conversation_with_different_agents(self, agent_factory):
        """Test conversation that involves different agents across turns."""
        from src.agents.factory import AgentRegistry
        from src.agents.base import Message, MessageRole

        registry = AgentRegistry(agent_factory)
        orchestrator = registry.get_agent(AgentType.ORCHESTRATOR)

        # Start conversation
        session = await agent_factory.context_manager.create_session(user_id="test-user")
        agent_context = AgentContext(conversation_context=session)

        # Conversation flow: Factual -> Architecture -> Planning
        conversation_turns = [
            ("What is microservices architecture?", AgentType.RAG_RETRIEVAL),
            ("Design a microservices system for e-commerce", [AgentType.TECHNICAL_ARCHITECT, AgentType.RAG_RETRIEVAL]),
            ("Break down the implementation into tasks", [AgentType.TASK_PLANNER, AgentType.RAG_RETRIEVAL]),
        ]

        for turn_num, (query, expected_agents) in enumerate(conversation_turns, 1):
            # Process query
            response = await orchestrator.process_query(query, agent_context)

            assert response is not None
            assert len(response.content) > 0

            # Check agent routing (allowing for fallback behavior)
            # Orchestrator always returns its own type, check primary agent in metadata
            assert response.agent_type == AgentType.ORCHESTRATOR
            if isinstance(expected_agents, list):
                assert response.metadata["primary_agent"] in [agent.value for agent in expected_agents]
            else:
                assert response.metadata["primary_agent"] == expected_agents.value

            # Note: Orchestrator automatically adds messages to conversation history
            # No need to manually add them here

            # Update context for next turn
            updated_session = await agent_factory.context_manager.get_context(session.session_id)
            agent_context.conversation_context = updated_session

            print(f"Turn {turn_num}: {query[:50]}... -> {response.metadata['primary_agent']}")

        # Verify final conversation state
        final_session = await agent_factory.context_manager.get_context(session.session_id)
        assert len(final_session.conversation_history) == len(conversation_turns) * 2  # User + Assistant per turn

    @pytest.mark.asyncio
    async def test_conversation_context_limits(self, agent_factory):
        """Test conversation context management with size limits."""
        from src.agents.base import Message, MessageRole

        # Create session
        session = await agent_factory.context_manager.create_session(user_id="test-user")

        # Add many messages to test context trimming
        for i in range(60):  # More than max_context_size (50)
            message = Message(
                role=MessageRole.USER if i % 2 == 0 else MessageRole.ASSISTANT,
                content=f"Message {i}: This is test content for context management."
            )
            await agent_factory.context_manager.add_message(session.session_id, message)

        # Verify context was trimmed
        final_session = await agent_factory.context_manager.get_context(session.session_id)
        assert len(final_session.conversation_history) <= 50  # Should be trimmed to max size

        # Verify most recent messages are preserved
        last_message = final_session.conversation_history[-1]
        assert "Message 59" in last_message.content

    @pytest.mark.asyncio
    async def test_conversation_error_handling(self, agent_factory):
        """Test conversation handling with errors and recovery."""
        from src.agents.factory import AgentRegistry
        from src.agents.base import Message, MessageRole

        registry = AgentRegistry(agent_factory)
        orchestrator = registry.get_agent(AgentType.ORCHESTRATOR)

        # Start conversation
        session = await agent_factory.context_manager.create_session(user_id="test-user")
        agent_context = AgentContext(conversation_context=session)

        # Test with a very complex query that might cause issues
        complex_query = "Design and implement a distributed microservices architecture with event sourcing, CQRS, and real-time analytics for a global e-commerce platform handling millions of transactions per day, including detailed security considerations, monitoring strategies, deployment pipelines, and disaster recovery procedures."

        try:
            response = await orchestrator.process_query(complex_query, agent_context)

            # Should get some response even if it's a fallback
            assert response is not None
            assert len(response.content) > 0
            assert response.confidence >= 0.0

        except Exception as e:
            # If there's an error, it should be handled gracefully
            pytest.fail(f"Orchestrator should handle complex queries gracefully, but got: {e}")

    @pytest.mark.asyncio
    async def test_session_isolation(self, agent_factory):
        """Test that different conversation sessions are properly isolated."""
        from src.agents.base import Message, MessageRole

        # Create two separate sessions
        session1 = await agent_factory.context_manager.create_session(user_id="user1")
        session2 = await agent_factory.context_manager.create_session(user_id="user2")

        assert session1.session_id != session2.session_id

        # Add different messages to each session
        msg1 = Message(role=MessageRole.USER, content="Session 1 message")
        msg2 = Message(role=MessageRole.USER, content="Session 2 message")

        await agent_factory.context_manager.add_message(session1.session_id, msg1)
        await agent_factory.context_manager.add_message(session2.session_id, msg2)

        # Verify isolation
        updated_session1 = await agent_factory.context_manager.get_context(session1.session_id)
        updated_session2 = await agent_factory.context_manager.get_context(session2.session_id)

        assert len(updated_session1.conversation_history) == 1
        assert len(updated_session2.conversation_history) == 1
        assert updated_session1.conversation_history[0].content == "Session 1 message"
        assert updated_session2.conversation_history[0].content == "Session 2 message"
