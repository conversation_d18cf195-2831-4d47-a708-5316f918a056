"""
Unit tests for Orchestrator Agent.

This module tests the OrchestratorAgent class including:
- Query processing and routing
- Agent coordination
- Response synthesis
- Error handling and fallbacks
- Conversation context management
"""

import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from src.orchestrator.orchestrator import OrchestratorAgent
from src.agents.base import (
    AgentType,
    AgentContext,
    AgentResponse,
    ConversationContext,
    Message,
    MessageRole,
)
from src.agents.exceptions import <PERSON><PERSON><PERSON><PERSON>, AgentTimeoutError
from src.orchestrator.router import RoutingDecision
from src.orchestrator.synthesizer import SynthesisResult


class TestOrchestratorAgent:
    """Test OrchestratorAgent functionality."""

    @pytest.fixture
    def mock_settings(self):
        """Create mock settings."""
        settings = MagicMock()
        settings.llm_timeout = 30
        settings.max_retries = 3
        return settings

    @pytest.fixture
    def mock_llm_client(self):
        """Create mock LLM client."""
        return AsyncMock()

    @pytest.fixture
    def mock_formatter(self):
        """Create mock formatter."""
        return MagicMock()

    @pytest.fixture
    def mock_context_manager(self):
        """Create mock context manager."""
        mock_manager = AsyncMock()
        mock_session = ConversationContext(session_id="test-session")
        mock_manager.create_session.return_value = mock_session
        mock_manager.get_context.return_value = mock_session
        mock_manager.add_message.return_value = mock_session
        return mock_manager

    @pytest.fixture
    def mock_agent_factory(self):
        """Create mock agent factory."""
        return MagicMock()

    @pytest.fixture
    def orchestrator(self, mock_settings, mock_llm_client, mock_formatter, mock_context_manager, mock_agent_factory):
        """Create orchestrator agent for testing."""
        return OrchestratorAgent(
            settings=mock_settings,
            llm_client=mock_llm_client,
            formatter=mock_formatter,
            context_manager=mock_context_manager,
            agent_factory=mock_agent_factory,
        )

    @pytest.fixture
    def agent_context(self):
        """Create test agent context."""
        conversation = ConversationContext(session_id="test-session", user_id="test-user")
        return AgentContext(conversation_context=conversation)

    def test_orchestrator_initialization(self, orchestrator):
        """Test orchestrator initialization."""
        assert orchestrator.agent_type == AgentType.ORCHESTRATOR
        assert orchestrator.classifier is not None
        assert orchestrator.router is not None
        assert orchestrator.synthesizer is not None
        assert orchestrator.max_agent_timeout == 30.0
        assert orchestrator.max_fallback_attempts == 3
        assert orchestrator.enable_multi_agent is True

    def test_can_handle_query(self, orchestrator, agent_context):
        """Test that orchestrator can handle any query."""
        confidence = orchestrator.can_handle_query("Any query", agent_context)
        assert confidence == 1.0

    @pytest.mark.asyncio
    async def test_process_query_success(self, orchestrator, agent_context):
        """Test successful query processing."""
        # Mock routing decision
        routing_decision = RoutingDecision(
            primary_agent=AgentType.RAG_RETRIEVAL,
            secondary_agents=[],
            routing_strategy="single_agent",
            confidence=0.9,
            reasoning="Test routing",
            fallback_agents=[],
        )
        orchestrator.router.route_query = MagicMock(return_value=routing_decision)

        # Mock agent response
        mock_agent = AsyncMock()
        mock_response = AgentResponse(
            agent_type=AgentType.RAG_RETRIEVAL,
            content="Test response",
            confidence=0.9,
            sources=["test.py"],
        )
        mock_agent.process_query.return_value = mock_response
        orchestrator._get_agent = MagicMock(return_value=mock_agent)

        # Mock synthesis
        synthesis_result = SynthesisResult(
            content="Synthesized response",
            confidence=0.9,
            sources=["test.py"],
            metadata={},
            synthesis_strategy="single_response",
        )
        orchestrator.synthesizer.synthesize_responses = MagicMock(return_value=synthesis_result)

        # Process query
        result = await orchestrator.process_query("Test query", agent_context)

        # Verify result
        assert result.agent_type == AgentType.ORCHESTRATOR
        assert result.content == "Synthesized response"
        assert result.confidence == 0.9
        assert "test.py" in result.sources
        assert result.metadata["primary_agent"] == AgentType.RAG_RETRIEVAL.value

    @pytest.mark.asyncio
    async def test_process_query_with_secondary_agents(self, orchestrator, agent_context):
        """Test query processing with secondary agents."""
        # Mock routing decision with secondary agents
        routing_decision = RoutingDecision(
            primary_agent=AgentType.TECHNICAL_ARCHITECT,
            secondary_agents=[AgentType.TASK_PLANNER],
            routing_strategy="multi_agent",
            confidence=0.8,
            reasoning="Multi-agent routing",
            fallback_agents=[],
        )
        orchestrator.router.route_query = MagicMock(return_value=routing_decision)

        # Mock primary agent
        primary_agent = AsyncMock()
        primary_response = AgentResponse(
            agent_type=AgentType.TECHNICAL_ARCHITECT,
            content="Architecture response",
            confidence=0.8,
        )
        primary_agent.process_query.return_value = primary_response

        # Mock secondary agent
        secondary_agent = AsyncMock()
        secondary_response = AgentResponse(
            agent_type=AgentType.TASK_PLANNER,
            content="Planning response",
            confidence=0.7,
        )
        secondary_agent.process_query.return_value = secondary_response

        # Mock agent factory
        def mock_get_agent(agent_type):
            if agent_type == AgentType.TECHNICAL_ARCHITECT:
                return primary_agent
            elif agent_type == AgentType.TASK_PLANNER:
                return secondary_agent
            return MagicMock()

        orchestrator._get_agent = mock_get_agent

        # Mock synthesis
        synthesis_result = SynthesisResult(
            content="Multi-agent response",
            confidence=0.75,
            sources=[],
            metadata={"agent_count": 2},
            synthesis_strategy="multi_agent_merge",
        )
        orchestrator.synthesizer.synthesize_responses = MagicMock(return_value=synthesis_result)

        # Process query
        result = await orchestrator.process_query("Complex query", agent_context)

        # Verify result
        assert result.agent_type == AgentType.ORCHESTRATOR
        assert result.content == "Multi-agent response"
        assert result.metadata["agent_count"] == 2
        assert result.metadata["primary_agent"] == AgentType.TECHNICAL_ARCHITECT.value

    @pytest.mark.asyncio
    async def test_process_query_with_fallback(self, orchestrator, agent_context):
        """Test query processing with fallback agents."""
        # Mock routing decision
        routing_decision = RoutingDecision(
            primary_agent=AgentType.TECHNICAL_ARCHITECT,
            secondary_agents=[],
            routing_strategy="fallback_chain",
            confidence=0.3,
            reasoning="Low confidence routing",
            fallback_agents=[AgentType.RAG_RETRIEVAL],
        )
        orchestrator.router.route_query = MagicMock(return_value=routing_decision)

        # Mock primary agent that fails
        primary_agent = AsyncMock()
        primary_agent.process_query.side_effect = AgentError("Primary agent failed")

        # Mock fallback agent
        fallback_agent = AsyncMock()
        fallback_response = AgentResponse(
            agent_type=AgentType.RAG_RETRIEVAL,
            content="Fallback response",
            confidence=0.6,
        )
        fallback_agent.process_query.return_value = fallback_response

        # Mock agent factory
        def mock_get_agent(agent_type):
            if agent_type == AgentType.TECHNICAL_ARCHITECT:
                return primary_agent
            elif agent_type == AgentType.RAG_RETRIEVAL:
                return fallback_agent
            return MagicMock()

        orchestrator._get_agent = mock_get_agent

        # Mock synthesis
        synthesis_result = SynthesisResult(
            content="Fallback response",
            confidence=0.6,
            sources=[],
            metadata={},
            synthesis_strategy="single_response",
        )
        orchestrator.synthesizer.synthesize_responses = MagicMock(return_value=synthesis_result)

        # Process query
        result = await orchestrator.process_query("Test query", agent_context)

        # Verify fallback was used
        assert result.agent_type == AgentType.ORCHESTRATOR
        assert result.content == "Fallback response"

    @pytest.mark.asyncio
    async def test_process_query_timeout_handling(self, orchestrator, agent_context):
        """Test timeout handling in query processing."""
        # Mock routing decision
        routing_decision = RoutingDecision(
            primary_agent=AgentType.RAG_RETRIEVAL,
            secondary_agents=[],
            routing_strategy="single_agent",
            confidence=0.9,
            reasoning="Test routing",
            fallback_agents=[],
        )
        orchestrator.router.route_query = MagicMock(return_value=routing_decision)

        # Mock agent that times out
        mock_agent = AsyncMock()
        mock_agent.process_query.side_effect = AgentTimeoutError("Agent timed out")
        orchestrator._get_agent = MagicMock(return_value=mock_agent)

        # Process query - should handle timeout gracefully
        result = await orchestrator.process_query("Test query", agent_context)

        # Should return fallback response
        assert result.agent_type == AgentType.ORCHESTRATOR
        assert "error" in result.content.lower()
        assert result.confidence == 0.1
        assert result.metadata.get("fallback_response") is True

    @pytest.mark.asyncio
    async def test_conversation_context_updates(self, orchestrator, agent_context):
        """Test conversation context updates."""
        # Mock successful processing
        routing_decision = RoutingDecision(
            primary_agent=AgentType.RAG_RETRIEVAL,
            secondary_agents=[],
            routing_strategy="single_agent",
            confidence=0.9,
            reasoning="Test routing",
            fallback_agents=[],
        )
        orchestrator.router.route_query = MagicMock(return_value=routing_decision)

        mock_agent = AsyncMock()
        mock_response = AgentResponse(
            agent_type=AgentType.RAG_RETRIEVAL,
            content="Test response",
            confidence=0.9,
        )
        mock_agent.process_query.return_value = mock_response
        orchestrator._get_agent = MagicMock(return_value=mock_agent)

        synthesis_result = SynthesisResult(
            content="Test response",
            confidence=0.9,
            sources=[],
            metadata={},
            synthesis_strategy="single_response",
        )
        orchestrator.synthesizer.synthesize_responses = MagicMock(return_value=synthesis_result)

        # Process query
        await orchestrator.process_query("Test query", agent_context)

        # Verify context manager was called to add messages
        assert orchestrator.context_manager.add_message.call_count == 2  # User + Assistant messages

    def test_get_available_agents(self, orchestrator):
        """Test getting available agents."""
        available_agents = orchestrator._get_available_agents()
        
        expected_agents = {
            AgentType.TECHNICAL_ARCHITECT,
            AgentType.TASK_PLANNER,
            AgentType.RAG_RETRIEVAL,
        }
        
        assert available_agents == expected_agents

    def test_get_orchestrator_stats(self, orchestrator):
        """Test getting orchestrator statistics."""
        # Mock component stats
        orchestrator.classifier.get_classification_stats = MagicMock(return_value={"total_classifications": 10})
        orchestrator.router.get_routing_stats = MagicMock(return_value={"total_routes": 8})
        orchestrator.synthesizer.get_synthesis_stats = MagicMock(return_value={"total_syntheses": 5})

        stats = orchestrator.get_orchestrator_stats()

        assert "total_requests" in stats
        assert "classifier_stats" in stats
        assert "router_stats" in stats
        assert "synthesizer_stats" in stats
        assert "configuration" in stats
        assert stats["configuration"]["max_agent_timeout"] == 30.0
